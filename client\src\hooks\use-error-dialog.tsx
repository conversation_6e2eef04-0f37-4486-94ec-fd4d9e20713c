import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

type ErrorDialogState = {
  open: boolean;
  title: React.ReactNode;
  description: React.ReactNode;
};

type ErrorDialogAction =
  | { type: "OPEN_ERROR_DIALOG"; title: React.ReactNode; description: React.ReactNode }
  | { type: "CLOSE_ERROR_DIALOG" };

type ErrorDialogDispatch = (action: ErrorDialogAction) => void;

const ErrorDialogContext = React.createContext<
  { state: ErrorDialogState; dispatch: ErrorDialogDispatch } | undefined
>(undefined);

function errorDialogReducer(state: ErrorDialogState, action: ErrorDialogAction): ErrorDialogState {
  switch (action.type) {
    case "OPEN_ERROR_DIALOG":
      return {
        ...state,
        open: true,
        title: action.title,
        description: action.description,
      };
    case "CLOSE_ERROR_DIALOG":
      return {
        ...state,
        open: false,
      };
    default:
      return state;
  }
}

export function ErrorDialogProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = React.useReducer(errorDialogReducer, {
    open: false,
    title: "",
    description: "",
  });

  return (
    <ErrorDialogContext.Provider value={{ state, dispatch }}>
      {children}
      <ErrorDialogComponent />
    </ErrorDialogContext.Provider>
  );
}

function ErrorDialogComponent() {
  const context = React.useContext(ErrorDialogContext);
  if (!context) {
    throw new Error("useErrorDialog must be used within an ErrorDialogProvider");
  }

  const { state, dispatch } = context;

  return (
    <AlertDialog open={state.open} onOpenChange={(open) => {
      if (!open) dispatch({ type: "CLOSE_ERROR_DIALOG" });
    }}>
      <AlertDialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <AlertDialogHeader>
          <AlertDialogTitle>{state.title}</AlertDialogTitle>
        </AlertDialogHeader>
        <div className="overflow-y-auto max-h-[70vh] pr-2">
          {/* Use AlertDialogDescription only for simple text content */}
          {typeof state.description === 'string' ? (
            <AlertDialogDescription>
              {state.description}
            </AlertDialogDescription>
          ) : (
            <div className="text-sm text-muted-foreground">
              {state.description}
            </div>
          )}
        </div>
        <AlertDialogFooter>
          <AlertDialogAction onClick={() => dispatch({ type: "CLOSE_ERROR_DIALOG" })}>
            OK
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export function useErrorDialog() {
  const context = React.useContext(ErrorDialogContext);
  if (!context) {
    throw new Error("useErrorDialog must be used within an ErrorDialogProvider");
  }

  const { dispatch } = context;

  const showError = React.useCallback(
    (title: React.ReactNode, description: React.ReactNode) => {
      dispatch({
        type: "OPEN_ERROR_DIALOG",
        title,
        description,
      });
    },
    [dispatch]
  );

  return { showError };
}
