// <PERSON>ript to directly create checkout pages in the database
import fs from 'fs';
import path from 'path';
import { nanoid } from 'nanoid';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the storage file
const storagePath = path.join(__dirname, 'server', 'data', 'storage.json');

// Read the current storage
let storage;
try {
  const data = fs.readFileSync(storagePath, 'utf8');
  storage = JSON.parse(data);

  // Initialize customCheckoutPages if it doesn't exist
  if (!storage.customCheckoutPages) {
    storage.customCheckoutPages = [];
  }

  // Create trial checkout page
  const trialSlug = `default-trial-checkout-${nanoid(6)}`;
  const trialCheckoutPage = {
    id: nanoid(),
    title: 'Default Trial Checkout',
    slug: trialSlug,
    productName: 'IPTV Trial Subscription',
    productDescription: 'Try our premium IPTV service for 24 hours with full access to all channels and features.',
    price: 4.99,
    imageUrl: '',
    paymentMethod: 'trial-custom-link',
    smtpProviderId: 'smtp-1',
    requireUsername: false,
    isTrialCheckout: true,
    active: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Create regular checkout page
  const regularSlug = `default-regular-checkout-${nanoid(6)}`;
  const regularCheckoutPage = {
    id: nanoid(),
    title: 'Default Regular Checkout',
    slug: regularSlug,
    productName: 'IPTV Premium Subscription',
    productDescription: 'Get access to our premium IPTV service with over 10,000 channels, VOD, and more.',
    price: 19.99,
    imageUrl: '',
    paymentMethod: 'custom-link',
    smtpProviderId: 'smtp-1',
    requireUsername: false,
    isTrialCheckout: false,
    active: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Add the checkout pages to storage
  storage.customCheckoutPages.push(trialCheckoutPage);
  storage.customCheckoutPages.push(regularCheckoutPage);

  // Write the updated storage back to the file
  fs.writeFileSync(storagePath, JSON.stringify(storage, null, 2), 'utf8');

  console.log('Created trial checkout page:', trialCheckoutPage);
  console.log('Created regular checkout page:', regularCheckoutPage);
  console.log('Checkout pages created successfully!');

  console.log('\nTrial checkout URL:', `/checkout/${trialSlug}`);
  console.log('Regular checkout URL:', `/checkout/${regularSlug}`);
} catch (error) {
  console.error('Error creating checkout pages:', error);
}
