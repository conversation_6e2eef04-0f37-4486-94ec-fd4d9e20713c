import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Search, Eye, Mail, Calendar as CalendarIcon, Filter,
  Download, MoreVertical, ExternalLink, FileText, Trash,
  CreditCard, CheckCircle
} from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import OrderDetails from '@/components/admin/OrderDetails';
import TextareaEmailEditor from '@/components/admin/TextareaEmailEditor';
import SimpleRichTextEditor from '@/components/admin/SimpleRichTextEditor';
import EnhancedEmailEditor from '@/components/admin/EnhancedEmailEditor';
import InvoiceViewer from '@/components/admin/InvoiceViewer';

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [isInvoiceViewerOpen, setIsInvoiceViewerOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<number | null>(null);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [templateName, setTemplateName] = useState('');
  const [emailTemplates, setEmailTemplates] = useState<any[]>([]);

  const { toast } = useToast();

  // Fetch email templates from the server
  const fetchEmailTemplates = async () => {
    try {
      const response = await fetch('/api/email-templates');

      if (!response.ok) {
        throw new Error('Failed to fetch email templates');
      }

      const data = await response.json();
      setEmailTemplates(data);
    } catch (error) {
      console.error('Error fetching email templates:', error);
    }
  };

  // Fetch email templates on component mount
  useEffect(() => {
    fetchEmailTemplates();
  }, []);
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState<any[]>([]);

  // Fetch orders from the server
  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/invoices');

      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }

      const data = await response.json();
      // Filter out trial orders - they should only appear in the Trial Orders section
      const regularOrders = data.filter((order: any) => !order.isTrialOrder);
      setOrders(regularOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast({
        title: "Error",
        description: "Failed to fetch orders. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch orders on component mount
  useEffect(() => {
    fetchOrders();
  }, []);

  // Apply filters to orders
  const getFilteredOrders = () => {
    if (!orders) return [];

    return orders.filter((order: any) => {
      // Filter by search term
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        searchTerm === '' ||
        order.customerName?.toLowerCase().includes(searchLower) ||
        order.customerEmail?.toLowerCase().includes(searchLower) ||
        (order.paypalInvoiceId && order.paypalInvoiceId.toLowerCase().includes(searchLower)) ||
        (order.productName && order.productName.toLowerCase().includes(searchLower));

      // Filter by status
      const matchesStatus =
        filterStatus === 'all' ||
        (filterStatus === 'pending' && ['draft', 'sent'].includes(order.status?.toLowerCase() || '')) ||
        (filterStatus === 'completed' && order.status?.toLowerCase() === 'paid') ||
        (filterStatus === 'cancelled' && ['cancelled', 'no_paypal'].includes(order.status?.toLowerCase() || ''));

      // Filter by date
      const matchesDate =
        !date ||
        new Date(order.createdAt).toDateString() === date.toDateString();

      return matchesSearch && matchesStatus && matchesDate;
    });
  };

  const filteredOrders = getFilteredOrders();

  // Group orders by status
  const pendingOrders = orders.filter((order: any) =>
    ['draft', 'sent'].includes(order.status?.toLowerCase() || ''));
  const completedOrders = orders.filter((order: any) =>
    order.status?.toLowerCase() === 'paid');
  const cancelledOrders = orders.filter((order: any) =>
    ['cancelled', 'no_paypal'].includes(order.status?.toLowerCase() || ''));

  // Compute stats
  const getStats = () => {
    if (!orders || orders.length === 0) return {
      total: 0,
      completed: 0,
      pending: 0,
      cancelled: 0,
      totalAmount: 0
    };

    const totalAmount = completedOrders.reduce((sum: number, order: any) =>
      sum + (typeof order.amount === 'string' ? parseFloat(order.amount) : order.amount), 0);

    return {
      total: orders.length,
      completed: completedOrders.length,
      pending: pendingOrders.length,
      cancelled: cancelledOrders.length,
      totalAmount: totalAmount
    };
  };

  const stats = getStats();

  // Format currency
  const formatCurrency = (amount: string | number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(typeof amount === 'string' ? parseFloat(amount) : amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase();
    if (statusLower === 'paid') return "success";
    if (['draft', 'sent'].includes(statusLower)) return "default";
    if (['cancelled', 'no_paypal'].includes(statusLower)) return "destructive";
    return "secondary";
  };

  // Handle view order details
  const handleViewOrder = (order: any) => {
    setSelectedOrder(order);
    setIsDetailsOpen(true);
  };

  // Handle opening email dialog
  const handleOpenEmailDialog = (order: any) => {
    setSelectedOrder(order);

    // Set default email subject and content
    const defaultSubject = `Your order #${order.id} from PayPal Invoicer`;
    const defaultContent = `<p>Hello ${order.customerName},</p>
<p>Thank you for your order #${order.id}.</p>
<p>Order details:</p>
<ul>
  <li>Product: ${order.productName || `Product #${order.productId}`}</li>
  <li>Amount: ${formatCurrency(order.amount)}</li>
  <li>Date: ${formatDate(order.createdAt)}</li>
  ${order.country ? `<li>Country: ${order.country}</li>` : ''}
  ${order.appType ? `<li>Application: ${order.appType}</li>` : ''}
  ${order.macAddress ? `<li>MAC Address: ${order.macAddress}</li>` : ''}
</ul>
<p>Lien: https://example.com/your-download-link</p>
<p>Thank you for your business!</p>
<p>Best regards,<br>PayPal Invoicer Team</p>`;

    setEmailSubject(defaultSubject);
    setEmailContent(defaultContent);
    setIsEmailDialogOpen(true);
  };

  // Handle CSV export
  const handleDownloadCSV = () => {
    if (filteredOrders.length === 0) {
      toast({
        title: "No orders to export",
        description: "There are no orders matching your current filters.",
        variant: "destructive"
      });
      return;
    }

    // Create CSV content
    const headers = ["ID", "Customer Name", "Email", "Product", "Amount", "Date", "Status"];
    const csvContent = [
      headers.join(","),
      ...filteredOrders.map((order: any) => [
        order.id,
        `"${order.customerName?.replace(/"/g, '""') || ''}"`,
        `"${order.customerEmail?.replace(/"/g, '""') || ''}"`,
        `"${(order.productName || `Product #${order.productId}`)?.replace(/"/g, '""')}"`,
        typeof order.amount === 'string' ? parseFloat(order.amount) : order.amount,
        format(new Date(order.createdAt), "yyyy-MM-dd"),
        order.status
      ].join(","))
    ].join("\n");

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `orders-export-${format(new Date(), "yyyy-MM-dd")}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export successful",
      description: `${filteredOrders.length} orders exported to CSV.`,
    });
  };

  // Handle opening PayPal invoice
  const handleOpenPayPalInvoice = (url: string | null) => {
    if (url) {
      window.open(url, '_blank');
    } else {
      toast({
        title: "No URL available",
        description: "This order doesn't have a PayPal URL",
        variant: "destructive"
      });
    }
  };

  // Handle viewing invoice
  const handleViewInvoice = (order: any) => {
    setSelectedInvoiceId(order.id);
    setIsInvoiceViewerOpen(true);
  };

  // Handle removing a customer
  const handleRemoveCustomer = (order: any) => {
    if (window.confirm(`Are you sure you want to remove customer ${order.customerName}? This action cannot be undone.`)) {
      fetch(`/api/admin/invoices/${order.id}`, {
        method: 'DELETE',
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to remove customer');
          }
          return response.json();
        })
        .then(() => {
          toast({
            title: "Success",
            description: `Customer ${order.customerName} has been removed`,
          });
          fetchOrders(); // Refresh the orders list
        })
        .catch(error => {
          console.error('Error removing customer:', error);
          toast({
            title: "Error",
            description: "Failed to remove customer. Please try again.",
            variant: "destructive"
          });
        });
    }
  };

  // Handle toggling payment status
  const handleTogglePaymentStatus = (order: any) => {
    const newStatus = order.status.toLowerCase() === 'paid' ? 'sent' : 'paid';
    const isBeingMarkedAsPaid = newStatus.toLowerCase() === 'paid';

    fetch(`/api/admin/invoices/${order.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: newStatus,
        // If we're marking this as paid, check if there's a trial order to upgrade
        checkForTrialUpgrade: isBeingMarkedAsPaid,
        customerEmail: order.customerEmail
      })
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to update payment status');
        }
        return response.json();
      })
      .then((data) => {
        toast({
          title: "Success",
          description: `Payment status updated to ${newStatus}`,
        });

        // If a trial order was upgraded, show a notification
        if (data.trialOrderUpgraded) {
          toast({
            title: "Trial Order Upgraded",
            description: `Trial order for ${order.customerName} has been automatically marked as upgraded`,
            variant: "success"
          });
        }

        fetchOrders(); // Refresh the orders list
      })
      .catch(error => {
        console.error('Error updating payment status:', error);
        toast({
          title: "Error",
          description: "Failed to update payment status. Please try again.",
          variant: "destructive"
        });
      });
  };

  // State for add to allowed emails checkbox
  const [addToAllowedEmails, setAddToAllowedEmails] = useState<boolean>(
    localStorage.getItem('addToAllowedEmails') === 'false' ? false : true
  );

  // Handle sending email
  const handleSendEmail = async () => {
    if (!selectedOrder || !emailSubject || !emailContent) {
      toast({
        title: "Missing information",
        description: "Please fill in all fields",
        variant: "destructive"
      });
      return;
    }

    try {
      // Get the current SMTP provider ID from the order if available
      const smtpProviderId = selectedOrder.smtpProviderId;
      console.log('Sending email using SMTP provider:', smtpProviderId || 'default');

      // Call the API to send the email
      const response = await fetch('/api/send-email/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: selectedOrder.id,
          to: selectedOrder.customerEmail,
          subject: emailSubject,
          content: emailContent,
          smtpProviderId: smtpProviderId,
          addToAllowedEmails: addToAllowedEmails
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send email');
      }

      // Close the dialog
      setIsEmailDialogOpen(false);

      // Refresh the orders list to show any updates
      await fetchOrders();

      // Show a success message
      toast({
        title: "Success",
        description: "Email sent successfully!",
      });

      // If the email was added to allowed emails, show a confirmation
      if (data.allowedEmailResult) {
        toast({
          title: "Email Added",
          description: `Email "${selectedOrder.customerEmail}" added to allowed list`,
        });
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to send email',
        variant: "destructive"
      });
    }
  };

  // Render order table
  const renderOrderTable = (orders: any[]) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Customer</TableHead>
          <TableHead>Checkout Page</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {orders.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
              {searchTerm || filterStatus !== 'all' || date ?
                'No orders match your filters. Try adjusting your search criteria.' :
                'No orders found. Orders will appear here once customers make purchases.'}
            </TableCell>
          </TableRow>
        ) : (
          orders.map((order: any) => (
            <TableRow key={order.id}>
              <TableCell>
                <div className="font-medium">{order.customerName}</div>
                <div className="text-sm text-muted-foreground">{order.customerEmail}</div>
                <div className="text-xs text-muted-foreground">ID: {order.id}</div>
                {order.country && (
                  <div className="text-xs text-blue-600 mt-1">
                    Country: {order.country}
                  </div>
                )}
              </TableCell>
              <TableCell>
                <div className="font-medium">
                  {order.checkoutPageTitle ? (
                    <span className="text-blue-600">{order.checkoutPageTitle}</span>
                  ) : (
                    <span className="text-muted-foreground">No Checkout Page</span>
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  Product: {order.productName || `Product #${order.productId}`}
                </div>
                {order.appType && (
                  <div className="text-xs text-blue-600 mt-1">
                    App: {order.appType}
                    {order.macAddress && ` (MAC: ${order.macAddress})`}
                  </div>
                )}
              </TableCell>
              <TableCell>{formatCurrency(order.amount)}</TableCell>
              <TableCell>{formatDate(order.createdAt)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusBadge(order.status)}>
                    {order.status}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTogglePaymentStatus(order);
                    }}
                    title={order.status.toLowerCase() === 'paid' ? 'Mark as Unpaid' : 'Mark as Paid'}
                    className={order.status.toLowerCase() === 'paid'
                      ? "text-green-600 hover:text-green-700 hover:bg-green-100"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-100"}
                  >
                    {order.status.toLowerCase() === 'paid'
                      ? <CheckCircle className="h-4 w-4" />
                      : <CreditCard className="h-4 w-4" />}
                  </Button>
                </div>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewOrder(order)}>
                      <Eye className="mr-2 h-4 w-4" /> View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleOpenEmailDialog(order)}>
                      <Mail className="mr-2 h-4 w-4" /> Send Email
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleViewInvoice(order)}>
                      <FileText className="mr-2 h-4 w-4" /> View Invoice
                    </DropdownMenuItem>
                    {order.paypalInvoiceUrl && (
                      <DropdownMenuItem onClick={() => handleOpenPayPalInvoice(order.paypalInvoiceUrl)}>
                        <ExternalLink className="mr-2 h-4 w-4" /> View PayPal Invoice
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onClick={() => handleRemoveCustomer(order)}
                      className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                    >
                      <Trash className="mr-2 h-4 w-4" /> Remove Customer
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );

  return (
    <AdminLayout>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Orders</h1>
            <p className="text-muted-foreground">Manage and view all customer orders</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Total Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-500">{stats.pending}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">${stats.totalAmount.toFixed(2)}</div>
            </CardContent>
          </Card>
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          </div>
        ) : (
          <Card className="shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-2xl font-bold">Order Management</CardTitle>
                <CardDescription>
                  View and manage all customer orders and invoices
                </CardDescription>
              </div>
              <Button onClick={handleDownloadCSV} className="ml-auto">
                <Download className="mr-2 h-4 w-4" /> Export CSV
              </Button>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="relative w-full md:w-1/3">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search customer or product..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className="w-full md:w-[240px] justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {date && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setDate(undefined)}
                    title="Clear date filter"
                  >
                    <Filter className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {renderOrderTable(filteredOrders)}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-gray-500">
                Showing {filteredOrders.length} of {orders.length} orders
              </div>
            </CardFooter>
          </Card>
        )}
      </div>

      {/* Order Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Order Details</DialogTitle>
            <DialogDescription>
              View and manage order information
            </DialogDescription>
          </DialogHeader>

          {selectedOrder && (
            <OrderDetails
              order={selectedOrder}
              onClose={() => setIsDetailsOpen(false)}
              onOrderUpdated={fetchOrders}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Email Dialog */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Send Email to Customer</DialogTitle>
            <DialogDescription>
              {selectedOrder && (
                <>Compose and send an email to {selectedOrder.customerName} ({selectedOrder.customerEmail})</>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="flex gap-4 mb-4">
            <div className="w-[200px] ml-auto">
              <Label htmlFor="templateSelect">Template</Label>
              <Select onValueChange={(value) => {
                const template = emailTemplates.find(t => t.id.toString() === value);
                if (template) {
                  setEmailSubject(template.subject);
                  setEmailContent(template.content);
                }
              }}>
                <SelectTrigger id="templateSelect" className="mt-1">
                  <SelectValue placeholder="Select template" />
                </SelectTrigger>
                <SelectContent>
                  {emailTemplates.map((template) => (
                    <SelectItem key={template.id} value={template.id.toString()}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <EnhancedEmailEditor
            subject={emailSubject}
            content={emailContent}
            onSubjectChange={setEmailSubject}
            onContentChange={setEmailContent}
            selectedOrder={selectedOrder}
            onSendEmail={handleSendEmail}
            onCancel={() => setIsEmailDialogOpen(false)}
            onSaveTemplate={() => setIsTemplateDialogOpen(true)}
            addToAllowedEmails={addToAllowedEmails}
            onAddToAllowedEmailsChange={setAddToAllowedEmails}
          />
        </DialogContent>
      </Dialog>

      {/* Save Template Dialog */}
      <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save as Template</DialogTitle>
            <DialogDescription>
              Save this email as a template for future use
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="templateName">Template Name</Label>
              <Input
                id="templateName"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                placeholder="e.g., Order Confirmation, Welcome Email"
                className="mt-1"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2 mt-4">
            <Button
              variant="outline"
              onClick={() => setIsTemplateDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (!templateName) {
                  alert('Please enter a template name');
                  return;
                }

                try {
                  // Save the template to the server
                  const response = await fetch('/api/admin/email/email-templates', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      name: templateName,
                      subject: emailSubject,
                      content: emailContent
                    }),
                  });

                  if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Failed to save template');
                  }

                  // Refresh the templates list
                  await fetchEmailTemplates();

                  // Reset form and close dialog
                  setTemplateName('');
                  setIsTemplateDialogOpen(false);

                  alert('Template saved successfully!');
                } catch (error) {
                  console.error('Error saving template:', error);
                  alert('Failed to save template. Please try again.');
                }
              }}
              disabled={!templateName}
            >
              Save Template
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Invoice Viewer Dialog */}
      <InvoiceViewer
        open={isInvoiceViewerOpen}
        onOpenChange={setIsInvoiceViewerOpen}
        invoiceId={selectedInvoiceId}
      />
    </AdminLayout>
  );
}
